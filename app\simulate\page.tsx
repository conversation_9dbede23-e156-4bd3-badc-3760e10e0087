export default function SimulatePage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-green-50 to-green-100">
      <div className="container mx-auto px-4 py-16">
        <h1 className="text-4xl font-bold text-green-900 mb-8">
          Climate Change Simulation
        </h1>
        
        <div className="bg-white rounded-lg p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-green-800 mb-4">
            Interactive Climate Scenarios
          </h2>
          <p className="text-gray-700 mb-4">
            This simulation tool allows you to explore how different climate change scenarios 
            affect ocean currents and thermohaline circulation patterns.
          </p>
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <p className="text-yellow-800">
              🚧 <strong>Coming Soon:</strong> Interactive climate simulation tools are currently under development. 
              This will include temperature rise scenarios, ice sheet melting effects, and their impact on ocean circulation.
            </p>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          <div className="bg-white rounded-lg p-6 shadow-lg">
            <h3 className="text-xl font-semibold text-green-800 mb-3">
              Temperature Scenarios
            </h3>
            <p className="text-gray-700">
              Model the effects of 1.5°C, 2°C, and 3°C global temperature increases 
              on ocean current strength and patterns.
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-lg">
            <h3 className="text-xl font-semibold text-green-800 mb-3">
              Ice Sheet Melting
            </h3>
            <p className="text-gray-700">
              Visualize how freshwater input from melting ice sheets affects 
              deep water formation and circulation patterns.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
