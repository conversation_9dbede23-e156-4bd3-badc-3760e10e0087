export default function TrackPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-purple-50 to-purple-100">
      <div className="container mx-auto px-4 py-16">
        <h1 className="text-4xl font-bold text-purple-900 mb-8">
          Ocean Research Tracking
        </h1>
        
        <div className="bg-white rounded-lg p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-purple-800 mb-4">
            Real-World Ocean Monitoring
          </h2>
          <p className="text-gray-700 mb-4">
            Track active ocean research missions, buoy networks, and autonomous underwater 
            vehicles collecting data on ocean currents and climate patterns.
          </p>
          <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
            <p className="text-blue-800">
              🌊 <strong>Coming Soon:</strong> Real-time tracking of research vessels, 
              ARGO float networks, and satellite data integration.
            </p>
          </div>
        </div>

        <div className="grid md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg p-6 shadow-lg">
            <h3 className="text-xl font-semibold text-purple-800 mb-3">
              🛰️ ARGO Float Network
            </h3>
            <p className="text-gray-700 text-sm">
              Track the global array of autonomous profiling floats measuring 
              temperature and salinity in the upper 2000m of the ocean.
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-lg">
            <h3 className="text-xl font-semibold text-purple-800 mb-3">
              🚢 Research Vessels
            </h3>
            <p className="text-gray-700 text-sm">
              Follow active oceanographic research missions and their 
              real-time data collection efforts.
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-lg">
            <h3 className="text-xl font-semibold text-purple-800 mb-3">
              📡 Satellite Data
            </h3>
            <p className="text-gray-700 text-sm">
              Monitor sea surface temperature, sea level, and other 
              ocean parameters from satellite observations.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
