import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-900 via-blue-800 to-blue-900">
      <div className="container mx-auto px-4 py-16">
        <header className="text-center mb-16">
          <h1 className="text-6xl font-bold text-white mb-6">
            Deep Currents
          </h1>
          <p className="text-xl text-blue-200 max-w-3xl mx-auto">
            Explore the hidden rivers of our oceans. Visualize global thermohaline circulation, 
            understand climate impacts, and discover the deep currents that shape our planet.
          </p>
        </header>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <Link href="/explore" className="group">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/20 transition-all duration-300">
              <h3 className="text-2xl font-semibold text-white mb-3">🌊 Explore</h3>
              <p className="text-blue-200">
                Interactive global map with real-time ocean current visualization and particle animations
              </p>
            </div>
          </Link>

          <Link href="/learn" className="group">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/20 transition-all duration-300">
              <h3 className="text-2xl font-semibold text-white mb-3">📚 Learn</h3>
              <p className="text-blue-200">
                Discover how thermohaline circulation works and why it's crucial for our climate
              </p>
            </div>
          </Link>

          <Link href="/simulate" className="group">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/20 transition-all duration-300">
              <h3 className="text-2xl font-semibold text-white mb-3">🔬 Simulate</h3>
              <p className="text-blue-200">
                Model climate change impacts on ocean currents with interactive scenarios
              </p>
            </div>
          </Link>

          <Link href="/track" className="group">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/20 transition-all duration-300">
              <h3 className="text-2xl font-semibold text-white mb-3">🛰️ Track</h3>
              <p className="text-blue-200">
                Follow real-world ocean research missions and buoy networks
              </p>
            </div>
          </Link>

          <Link href="/about" className="group">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 hover:bg-white/20 transition-all duration-300">
              <h3 className="text-2xl font-semibold text-white mb-3">ℹ️ About</h3>
              <p className="text-blue-200">
                Learn about our data sources, methodology, and the science behind the visualization
              </p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}

