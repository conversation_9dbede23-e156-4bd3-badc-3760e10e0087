{"name": "deepdrift", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "19.1.0", "react-dom": "19.1.0", "next": "15.4.1", "deck.gl": "^9.0.0", "@deck.gl/react": "^9.0.0", "@deck.gl/layers": "^9.0.0", "@deck.gl/geo-layers": "^9.0.0", "@deck.gl/aggregation-layers": "^9.0.0", "react-map-gl": "^7.1.0", "mapbox-gl": "^3.0.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.1", "@eslint/eslintrc": "^3"}}