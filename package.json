{"name": "deepdrift", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@deck.gl/aggregation-layers": "^9.0.0", "@deck.gl/geo-layers": "^9.0.0", "@deck.gl/layers": "^9.0.0", "@deck.gl/react": "^9.0.0", "deck.gl": "^9.1.13", "mapbox-gl": "^3.0.0", "next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0", "react-map-gl": "^7.1.9"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}