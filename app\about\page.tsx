export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      <div className="container mx-auto px-4 py-16">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">
          About Deep Currents
        </h1>
        
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <div className="bg-white rounded-lg p-6 shadow-lg">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">
              Our Mission
            </h2>
            <p className="text-gray-700 mb-4">
              Deep Currents aims to make ocean science accessible through interactive 
              visualizations of global thermohaline circulation and its critical role 
              in Earth's climate system.
            </p>
            <p className="text-gray-700">
              By combining real oceanographic data with cutting-edge visualization 
              technology, we help users understand the "conveyor belt" of ocean 
              currents that regulate our planet's climate.
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-lg">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">
              Technology Stack
            </h2>
            <ul className="text-gray-700 space-y-2">
              <li>• <strong>Next.js 15</strong> - React framework for web applications</li>
              <li>• <strong>Deck.gl</strong> - WebGL-powered data visualization</li>
              <li>• <strong>Mapbox GL JS</strong> - Interactive maps and geospatial data</li>
              <li>• <strong>TypeScript</strong> - Type-safe development</li>
              <li>• <strong>Tailwind CSS</strong> - Utility-first styling</li>
            </ul>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-lg mb-8">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">
            Data Sources
          </h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">Current Data Sources</h3>
              <ul className="text-gray-700 space-y-1 text-sm">
                <li>• NOAA Ocean Current Data</li>
                <li>• ARGO Float Network</li>
                <li>• World Ocean Atlas</li>
                <li>• Satellite Altimetry Data</li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">Planned Integrations</h3>
              <ul className="text-gray-700 space-y-1 text-sm">
                <li>• Real-time buoy networks</li>
                <li>• Climate model outputs</li>
                <li>• Research vessel tracking</li>
                <li>• Historical oceanographic data</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 border-l-4 border-blue-400 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-800 mb-2">
            Open Source & Educational Use
          </h3>
          <p className="text-blue-700">
            This project is designed for educational purposes and to promote 
            understanding of ocean science. We encourage use in classrooms, 
            research, and public education initiatives.
          </p>
        </div>
      </div>
    </div>
  );
}
