'use client';

import { useState, useEffect } from 'react';
import DeckG<PERSON> from '@deck.gl/react';
import { Map } from 'react-map-gl';
import { ScatterplotLayer } from '@deck.gl/layers';
import ErrorBoundary from '../components/ErrorBoundary';
import LoadingSpinner from '../components/LoadingSpinner';

interface ViewState {
  longitude: number;
  latitude: number;
  zoom: number;
  pitch: number;
  bearing: number;
}

interface OceanCurrentData {
  position: [number, number];
  speed: number;
  name: string;
}

const INITIAL_VIEW_STATE: ViewState = {
  longitude: 0,
  latitude: 20,
  zoom: 2,
  pitch: 0,
  bearing: 0
};

// Sample ocean current data points
const SAMPLE_DATA: OceanCurrentData[] = [
  { position: [-80, 25], speed: 2.5, name: 'Gulf Stream' },
  { position: [140, 35], speed: 1.8, name: 'Kuroshio Current' },
  { position: [-10, 60], speed: 1.2, name: 'North Atlantic Current' },
  { position: [20, -40], speed: 2.0, name: 'Agulhas Current' },
];

function MapErrorFallback({ error, resetError }: { error?: Error; resetError: () => void }) {
  return (
    <div className="h-screen flex items-center justify-center bg-red-50">
      <div className="text-center">
        <h2 className="text-xl font-semibold text-red-800 mb-2">Map Loading Error</h2>
        <p className="text-red-600 mb-4">
          {error?.message?.includes('mapbox')
            ? 'Please check your Mapbox token configuration.'
            : 'Failed to load the map visualization.'}
        </p>
        <button
          onClick={resetError}
          className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
        >
          Try Again
        </button>
      </div>
    </div>
  );
}

export default function ExplorePage() {
  const [viewState, setViewState] = useState<ViewState>(INITIAL_VIEW_STATE);
  const [showSurface, setShowSurface] = useState(true);
  const [showDeep, setShowDeep] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [mapError, setMapError] = useState<string | null>(null);

  useEffect(() => {
    // Check if Mapbox token is available
    if (!process.env.NEXT_PUBLIC_MAPBOX_TOKEN) {
      setMapError('Mapbox token is not configured. Please add NEXT_PUBLIC_MAPBOX_TOKEN to your environment variables.');
      setIsLoading(false);
      return;
    }

    // Simulate loading time for demonstration
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const layers = [
    new ScatterplotLayer<OceanCurrentData>({
      id: 'ocean-currents',
      data: SAMPLE_DATA,
      getPosition: (d: OceanCurrentData) => d.position,
      getRadius: (d: OceanCurrentData) => d.speed * 50000,
      getFillColor: () => [0, 150, 255, 180],
      pickable: true,
      radiusMinPixels: 5,
      radiusMaxPixels: 50,
    })
  ];

  // Show error state if there's a configuration issue
  if (mapError) {
    return (
      <div className="h-screen flex items-center justify-center bg-red-50">
        <div className="text-center max-w-md">
          <h2 className="text-xl font-semibold text-red-800 mb-2">Configuration Error</h2>
          <p className="text-red-600 mb-4">{mapError}</p>
          <p className="text-sm text-gray-600">
            Please check the README for setup instructions.
          </p>
        </div>
      </div>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-blue-900">
        <LoadingSpinner size="lg" color="white" text="Loading ocean visualization..." />
      </div>
    );
  }

  return (
    <ErrorBoundary fallback={MapErrorFallback}>
      <div className="h-screen relative">
        <DeckGL
          initialViewState={INITIAL_VIEW_STATE}
          controller={true}
          layers={layers}
          onViewStateChange={({viewState}: {viewState: ViewState}) => setViewState(viewState)}
        >
          <Map
            mapStyle="mapbox://styles/mapbox/dark-v11"
            mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_TOKEN}
          />
        </DeckGL>

      {/* Control Panel */}
      <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg">
        <h3 className="font-semibold mb-3">Ocean Layers</h3>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={showSurface}
              onChange={(e) => setShowSurface(e.target.checked)}
              className="mr-2"
            />
            Surface Currents
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={showDeep}
              onChange={(e) => setShowDeep(e.target.checked)}
              className="mr-2"
            />
            Deep Currents
          </label>
        </div>
      </div>

      {/* Info Panel */}
      <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg max-w-sm">
        <h3 className="font-semibold mb-2">Global Ocean Currents</h3>
        <p className="text-sm text-gray-600">
          Click on current markers to learn more about their characteristics and impact on global climate.
        </p>
      </div>
    </div>
    </ErrorBoundary>
  );
}