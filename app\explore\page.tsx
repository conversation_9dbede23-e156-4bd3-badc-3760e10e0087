'use client';

import { useState } from 'react';
import DeckGL from '@deck.gl/react';
import { Map } from 'react-map-gl';
import { ScatterplotLayer } from '@deck.gl/layers';

const INITIAL_VIEW_STATE = {
  longitude: 0,
  latitude: 20,
  zoom: 2,
  pitch: 0,
  bearing: 0
};

// Sample ocean current data points
const SAMPLE_DATA = [
  { position: [-80, 25], speed: 2.5, name: 'Gulf Stream' },
  { position: [140, 35], speed: 1.8, name: 'Kuroshio Current' },
  { position: [-10, 60], speed: 1.2, name: 'North Atlantic Current' },
  { position: [20, -40], speed: 2.0, name: 'Agulhas Current' },
];

export default function ExplorePage() {
  const [viewState, setViewState] = useState(INITIAL_VIEW_STATE);
  const [showSurface, setShowSurface] = useState(true);
  const [showDeep, setShowDeep] = useState(false);

  const layers = [
    new ScatterplotLayer({
      id: 'ocean-currents',
      data: SAMPLE_DATA,
      getPosition: (d: any) => d.position,
      getRadius: (d: any) => d.speed * 50000,
      getFillColor: (d: any) => [0, 150, 255, 180],
      pickable: true,
      radiusMinPixels: 5,
      radiusMaxPixels: 50,
    })
  ];

  return (
    <div className="h-screen relative">
      <DeckGL
        initialViewState={INITIAL_VIEW_STATE}
        controller={true}
        layers={layers}
        onViewStateChange={({viewState}) => setViewState(viewState)}
      >
        <Map
          mapStyle="mapbox://styles/mapbox/dark-v11"
          mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_TOKEN}
        />
      </DeckGL>

      {/* Control Panel */}
      <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg">
        <h3 className="font-semibold mb-3">Ocean Layers</h3>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={showSurface}
              onChange={(e) => setShowSurface(e.target.checked)}
              className="mr-2"
            />
            Surface Currents
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={showDeep}
              onChange={(e) => setShowDeep(e.target.checked)}
              className="mr-2"
            />
            Deep Currents
          </label>
        </div>
      </div>

      {/* Info Panel */}
      <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-4 shadow-lg max-w-sm">
        <h3 className="font-semibold mb-2">Global Ocean Currents</h3>
        <p className="text-sm text-gray-600">
          Click on current markers to learn more about their characteristics and impact on global climate.
        </p>
      </div>
    </div>
  );
}